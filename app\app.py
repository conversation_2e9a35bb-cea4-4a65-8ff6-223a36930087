


# app.py
from flask import Flask, render_template, request, jsonify, Response, session, redirect, url_for, flash, send_from_directory
from datetime import datetime, timedelta, timezone
import os
import csv
from io import StringIO
from dotenv import load_dotenv
from supabase.client import create_client, Client
from functools import wraps
import hashlib
import logging
import traceback
from typing import Optional, Dict, Any, List, Union

# Configure logging
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Load environment variables
load_dotenv()
# If you want to enforce loading a .env in the same folder as this file:
load_dotenv(os.path.join(os.path.dirname(__file__), '.env'), override=True)

app = Flask(__name__)
app.secret_key = os.getenv('SECRET_KEY', 'your-secret-key-change-this-in-production')

# Define IST timezone
IST = timezone(timedelta(hours=5, minutes=30))

def get_ist_now() -> datetime:
    """Get current time in IST"""
    return datetime.now(IST)

def convert_to_ist(dt_string: str) -> Optional[datetime]:
    """Convert ISO string to IST datetime with better error handling"""
    try:
        # Handle different timestamp formats
        if dt_string.endswith('Z'):
            # UTC with Z
            return datetime.fromisoformat(dt_string.replace('Z', '+00:00')).astimezone(IST)
        elif '+05:30' in dt_string:
            # Already IST format
            return datetime.fromisoformat(dt_string)
        elif '+00:00' in dt_string:
            # UTC explicit
            return datetime.fromisoformat(dt_string).astimezone(IST)
        else:
            # No timezone - assume IST
            dt = datetime.fromisoformat(dt_string)
            return dt.replace(tzinfo=IST) if dt.tzinfo is None else dt.astimezone(IST)
    except Exception as e:
        logger.error(f"Error converting timestamp {dt_string}: {e}")
        return None

def format_ist_datetime(dt: Union[str, datetime, None]) -> str:
    """Format datetime in IST format with safety checks"""
    if dt is None:
        return "Invalid date"
    
    if isinstance(dt, str):
        dt = convert_to_ist(dt)
    
    return dt.strftime('%Y-%m-%d %H:%M:%S IST') if dt else "Invalid date"

def ist_to_iso(dt: Optional[datetime]) -> Optional[str]:
    """Convert IST datetime to ISO string for database storage"""
    if dt is None:
        return None
    return dt.isoformat() if isinstance(dt, datetime) else dt

# Initialize Supabase client with robust error handling and fallback
supabase: Optional[Client] = None
mock_events = []

try:
    SUPABASE_URL = os.getenv("SUPABASE_URL", 'https://zvfltkbciwppawghqpdl.supabase.co')
    SUPABASE_KEY = os.getenv("SUPABASE_KEY", 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inp2Zmx0a2JjaXdwcGF3Z2hxcGRsIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTA2MDI0NjYsImV4cCI6MjA2NjE3ODQ2Nn0.z_U2VOsbP1lMK88dDp7xmVTLkQQdO9hvI8s47JfpspE')

    logger.info(f"Attempting to connect to Supabase at {SUPABASE_URL}")

    # Try to create Supabase client
    supabase = create_client(SUPABASE_URL, SUPABASE_KEY)
    
    # Test the connection
    if supabase:
        test_response = supabase.table('machine_events').select("*").limit(1).execute()
        if getattr(test_response, 'data', None) is not None:
            logger.info("✅ Supabase connected successfully")
            logger.info(f"Found {len(test_response.data)} records in test query.")
        else:
            logger.warning("Supabase connected, but test query failed.")
            supabase = None # Connection is not usable
    else:
        logger.warning("Supabase client creation failed.")
        supabase = None
except Exception as e:
    logger.error(f"❌ Supabase initialization failed: {str(e)}")
    logger.error(traceback.format_exc())
    supabase = None

# If Supabase failed, create mock data for offline mode
if supabase is None:
    logger.info("🔄 Running in OFFLINE MODE with mock data")
    import random

    # Generate realistic mock data
    base_time = datetime.now() - timedelta(days=7)
    machine_ids = ['machine_001', 'machine_002', 'machine_003']

    for i in range(100):
        machine_id = random.choice(machine_ids)
        start_time = base_time + timedelta(hours=random.randint(0, 168), minutes=random.randint(0, 59))
        duration = random.randint(300, 7200)  # 5 minutes to 2 hours
        end_time = start_time + timedelta(seconds=duration)

        mock_events.append({
            'id': i + 1,
            'start_time': start_time.isoformat(),
            'end_time': end_time.isoformat(),
            'duration': duration,
            'machine_id': machine_id,
            'created_at': start_time.isoformat()
        })

    mock_events.sort(key=lambda x: x['start_time'], reverse=True)

# API key for authentication
API_KEY = os.getenv('API_KEY', 'your-default-api-key-here')

# User authentication system
USERS: Dict[str, Dict[str, Any]] = {
    'admin': {
        'password': hashlib.sha256('admin123'.encode()).hexdigest(),
        'machine_ids': ['machine_001', 'machine_002', 'machine_003', '1', '2', '3'],
        'machine_name': 'All Machines',
        'role': 'admin'
    },
    'pankaj': {
        'password': hashlib.sha256('pankaj123'.encode()).hexdigest(),
        'machine_ids': ['machine_001', 'machine_002', 'machine_003', '1', '2', '3'],
        'machine_name': 'All Machines',
        'role': 'admin'
    },
    'abhi': {
        'password': hashlib.sha256('abhi123'.encode()).hexdigest(),
        'machine_ids': ['machine_001', 'machine_002', 'machine_003', '1', '2', '3'],
        'machine_name': 'All Machines',
        'role': 'admin'
    },
    'ankur_admin': {
        'password': hashlib.sha256('ankur@2023'.encode()).hexdigest(),
        'machine_ids': ['machine_001', 'machine_002', 'machine_003', '1', '2', '3'],
        'machine_name': 'All Production Lines',
        'role': 'admin'
    },
    'production_manager': {
        'password': hashlib.sha256('prod@123'.encode()).hexdigest(),
        'machine_ids': ['1', '2'],
        'machine_name': 'Production Lines A & B',
        'role': 'user'
    },
    'line_a_operator': {
        'password': hashlib.sha256('lineA@2023'.encode()).hexdigest(),
        'machine_ids': ['1'],
        'machine_name': 'machine_1',
        'role': 'user'
    },
    'line_b_operator': {
        'password': hashlib.sha256('lineB@2023'.encode()).hexdigest(),
        'machine_ids': ['2'],
        'machine_name': 'machine_2',
        'role': 'user'
    },
    'line_c_operator': {
        'password': hashlib.sha256('lineC@2023'.encode()).hexdigest(),
        'machine_ids': ['3'],
        'machine_name': 'machine_3',
        'role': 'user'
    },
    'user1': {
        'password': hashlib.sha256('password1'.encode()).hexdigest(),
        'machine_ids': ['1'],
        'machine_name': 'machine_1',
        'role': 'user'
    },
    'user2': {
        'password': hashlib.sha256('password2'.encode()).hexdigest(),
        'machine_ids': ['2'],
        'machine_name': 'machine_2',
        'role': 'user'
    },
    'user3': {
        'password': hashlib.sha256('password3'.encode()).hexdigest(),
        'machine_ids': ['3'],
        'machine_name': 'machine_3',
        'role': 'user'
    }
}

def login_required(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if 'username' not in session:
            if request.path.startswith('/api/'):
                return jsonify({'error': 'Unauthorized', 'message': 'Please login'}), 401
            return redirect(url_for('login'))
        return f(*args, **kwargs)
    return decorated_function

def get_user_machine_filter() -> List[str]:
    """Get machine filter for current user"""
    if 'username' not in session:
        return []
    user = USERS.get(session['username'])
    if not user:
        return []
    return user.get('machine_ids', [])

def add_user(username: str, password: str, machine_ids: List[str], machine_name: str, role: str = 'user') -> None:
    USERS[username] = {
        'password': hashlib.sha256(password.encode()).hexdigest(),
        'machine_ids': machine_ids,
        'machine_name': machine_name,
        'role': role
    }
    logger.info(f"User '{username}' added successfully with access to {machine_name}")

def generate_password_hash(password: str) -> str:
    return hashlib.sha256(password.encode()).hexdigest()

# Enhanced error handling for all API endpoints
def handle_api_errors(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        try:
            return f(*args, **kwargs)
        except Exception as e:
            logger.error(f"Error in {f.__name__}: {str(e)}")
            logger.error(traceback.format_exc())
            return jsonify({
                'error': 'Internal server error',
                'message': str(e),
                'endpoint': f.__name__
            }), 500
    return decorated_function

@app.context_processor
def inject_now() -> Dict[str, datetime]:
    return {'now': get_ist_now()}

@app.route('/login', methods=['GET', 'POST'])
def login():
    if request.method == 'POST':
        username = request.form['username']
        password = request.form['password']
        password_hash = hashlib.sha256(password.encode()).hexdigest()
        
        if username in USERS and USERS[username]['password'] == password_hash:
            session['username'] = username
            session['machine_ids'] = USERS[username]['machine_ids']
            session['machine_name'] = USERS[username]['machine_name']
            session['role'] = USERS[username]['role']
            flash(f'Welcome back, {username}!', 'success')
            return redirect(url_for('dashboard'))
        else:
            flash('Invalid username or password', 'error')
    return render_template('login.html')

@app.route('/logout')
def logout():
    username = session.get('username', 'User')
    session.clear()
    flash(f'Goodbye, {username}! You have been logged out.', 'info')
    return redirect(url_for('login'))

@app.route('/favicon.ico')
def favicon():
    try:
        return send_from_directory(
            os.path.join(app.root_path, 'static'),
            'favicon.svg',
            mimetype='image/svg+xml'
        )
    except FileNotFoundError:
        return '', 204

@app.route('/')
@login_required
def index():
    return redirect(url_for('dashboard'))

@app.route('/dashboard')
@login_required
def dashboard():
    # Get list of machines accessible to the user
    machine_ids = get_user_machine_filter()

    # Filter to only show Arduino format machines in the dropdown
    # Keep backend functionality intact by using all machine_ids for data access
    arduino_format_machines = ['machine_001', 'machine_002', 'machine_003']
    display_machine_ids = [mid for mid in machine_ids if mid in arduino_format_machines]

    machine_name_map = {
        'machine_001': 'machine_001',
        'machine_002': 'machine_002',
        'machine_003': 'machine_003',
        '1': 'machine_1',
        '2': 'machine_2',
        '3': 'machine_3',
        'machine_1': 'machine_1',  # Keep backward compatibility
        'machine_2': 'machine_2',
        'machine_3': 'machine_3',
        'ESP32_GPIO_001': 'ESP32_GPIO_001',
        'ESP32_CURRENT_001': 'ESP32_CURRENT_001',
        'ESP32_CURRENT_002': 'ESP32_CURRENT_002'
    }

    # Create list of machine names for the dropdown (only Arduino format)
    machine_options = [{'id': mid, 'name': machine_name_map.get(mid, mid)} for mid in display_machine_ids]

    # If user has access to only one machine, preselect it
    selected_machine = display_machine_ids[0] if display_machine_ids else None

    return render_template('dashboard.html',
                           now=get_ist_now(),
                           machine_options=machine_options,
                           selected_machine=selected_machine)

@app.route('/events')
@login_required
def events():
    return render_template('events.html')

@app.route('/test')
@login_required
def test_page():
    return render_template('test.html')

@app.route('/profile')
@login_required
def profile():
    user_info = USERS.get(session['username'], {})
    return render_template('profile.html', user_info=user_info)

@app.route('/settings')
@login_required
def settings():
    return render_template('settings.html')

@app.route('/admin/users')
@login_required
def admin_users():
    if session.get('role') != 'admin':
        flash('Access denied. Admin privileges required.', 'error')
        return redirect(url_for('dashboard'))
    
    users_info = {}
    for username, user_data in USERS.items():
        users_info[username] = {
            'machine_ids': user_data['machine_ids'],
            'machine_name': user_data['machine_name'],
            'role': user_data['role']
        }
    return render_template('admin_users.html', users=users_info)

@app.route('/api/admin/add-user', methods=['POST'])
@login_required
@handle_api_errors
def api_add_user():
    if session.get('role') != 'admin':
        return jsonify({'error': 'Access denied'}), 403
    
    data = request.get_json()
    try:
        username = data['username']
        password = data['password']
        machine_ids = data['machine_ids']
        machine_name = data['machine_name']
        role = data.get('role', 'user')

        if username in USERS:
            return jsonify({'error': 'Username already exists'}), 400
        
        add_user(username, password, machine_ids, machine_name, role)
        return jsonify({'message': f'User {username} added successfully'}), 200
    except KeyError as e:
        return jsonify({'error': f'Missing field: {e}'}), 400

@app.route('/api/test-ist')
@handle_api_errors
def test_ist():
    current_utc = datetime.now(timezone.utc)
    current_ist = get_ist_now()
    
    test_cases = [
        "2023-12-25T10:30:45.000Z",
        "2023-12-25T16:00:45+05:30",
        "2023-12-25T10:30:45+00:00",
        "2023-12-25T10:30:45"
    ]
    
    conversions = {}
    for test_case in test_cases:
        converted = convert_to_ist(test_case)
        formatted = format_ist_datetime(test_case)
        conversions[test_case] = {
            'converted_ist': converted.isoformat() if converted else None,
            'formatted': formatted
        }
    
    return jsonify({
        'current_utc': current_utc.isoformat(),
        'current_ist': current_ist.isoformat(),
        'current_ist_formatted': format_ist_datetime(current_ist.isoformat()),
        'test_conversions': conversions,
        'timezone_info': 'IST = UTC + 5:30'
    }), 200

@app.route('/api/supabase/setup')
@handle_api_errors
def setup_supabase():
    if supabase is None:
        return jsonify({'error': 'Database not available'}), 500
    
    test_response = supabase.table('machine_events').select("*").limit(1).execute()
    sample_data = supabase.table('machine_events').select("*").limit(5).execute()
    
    machine_counts = {}
    for machine_id in ['1', '2', '3', 'machine_001', 'machine_002', 'machine_003']:
        count_resp = supabase.table('machine_events') \
            .select("*") \
            .eq('machine_id', machine_id) \
            .execute()
        machine_counts[machine_id] = len(count_resp.data) if count_resp.data else 0
    
    return jsonify({
        'status': 'Connected to Supabase successfully',
        'table': 'machine_events',
        'total_records': len(test_response.data) if test_response.data else 0,
        'machine_counts': machine_counts,
        'sample_data': sample_data.data[:3] if sample_data.data else [],
        'table_structure': {
            'columns': [
                'id (auto-generated)',
                'start_time (timestamp)',
                'end_time (timestamp)',
                'duration (integer seconds)',
                'machine_id (text)',
                'created_at (auto-generated)'
            ]
        },
        'machine_mapping': {
            '1': 'machine_1',
            '2': 'machine_2',
            '3': 'machine_3',
            'machine_001': 'machine_1',  # Arduino format
            'machine_002': 'machine_2',
            'machine_003': 'machine_3',
            'machine_1': 'machine_1',  # Keep backward compatibility
            'machine_2': 'machine_2',
            'machine_3': 'machine_3'
        }
    }), 200

@app.route('/api/test-logo')
@handle_api_errors
def test_logo():
    static_folder = app.static_folder
    if not static_folder or not os.path.exists(static_folder):
        return jsonify({
            'error': 'Static folder not found',
            'static_folder': static_folder
        }), 500

    logo_path = os.path.join(static_folder, 'images', 'logo.jpeg')
    logo_exists = os.path.exists(logo_path)
    
    file_info = {}
    if logo_exists:
        file_info = {
            'size': os.path.getsize(logo_path),
            'path': logo_path,
            'readable': os.access(logo_path, os.R_OK)
        }
    
    images_folder = os.path.join(static_folder, 'images')
    images_folder_exists = os.path.exists(images_folder)
    images_folder_contents = os.listdir(images_folder) if images_folder_exists else []
    
    return jsonify({
        'logo_file_exists': logo_exists,
        'logo_path': logo_path,
        'file_info': file_info,
        'static_folder': static_folder,
        'images_folder_exists': images_folder_exists,
        'images_folder_contents': images_folder_contents
    }), 200

# CSV Export Helper Functions
def generate_events_csv(events: List[Dict[str, Any]]) -> str:
    csv_data = StringIO()
    writer = csv.writer(csv_data)
    writer.writerow(['Event ID', 'Start Time (IST)', 'End Time (IST)', 'Duration (s)', 'Duration (min)'])
    
    for event in events:
        writer.writerow([
            event.get('id', 'N/A'),
            format_ist_datetime(event.get('start_time')),
            format_ist_datetime(event.get('end_time')),
            event.get('duration', 0),
            round(event.get('duration', 0) / 60, 2)
        ])
    return csv_data.getvalue()

def generate_summary_csv(summary: Dict[str, Any]) -> str:
    csv_data = StringIO()
    writer = csv.writer(csv_data)
    writer.writerow(['Metric', 'Value'])
    writer.writerow(['Total Events', summary.get('total_events', 0)])
    writer.writerow(['Total Runtime (hours)', round(summary.get('total_runtime', 0) / 3600, 2)])
    
    if summary.get('last_event'):
        last_event = summary['last_event']
        writer.writerow(['Last Event Start (IST)', format_ist_datetime(last_event.get('start_time'))])
        writer.writerow(['Last Event End (IST)', format_ist_datetime(last_event.get('end_time'))])
        writer.writerow(['Last Event Duration (min)', round(last_event.get('duration', 0) / 60, 2)])
    
    return csv_data.getvalue()

def generate_chart_csv(labels: List[str], data: List[Any], title: str) -> str:
    csv_data = StringIO()
    writer = csv.writer(csv_data)
    writer.writerow([title, 'Value'])
    for i in range(len(labels)):
        writer.writerow([labels[i], data[i]])
    return csv_data.getvalue()

@app.route('/api/test-data', methods=['POST'])
@handle_api_errors
def send_test_data():
    if supabase is None:
        return jsonify({'error': 'Database not available'}), 500
    
    now = get_ist_now()
    test_events = []
    
    for i in range(5):
        start_time = now - timedelta(minutes=30 + i*10)
        duration = [300, 600, 900, 1200, 1800][i]
        end_time = start_time + timedelta(seconds=duration)
        
        event_data = {
            "start_time": ist_to_iso(start_time),
            "end_time": ist_to_iso(end_time),
            "duration": duration
        }
        
        response = supabase.table('machine_events').insert(event_data).execute()
        if response.data:
            test_events.append(event_data)
    
    return jsonify({
        'message': f'Added {len(test_events)} test events',
        'events': test_events
    }), 200

@app.route('/api/test-hardware', methods=['POST'])
@handle_api_errors
def test_hardware():
    data = request.get_json()
    validation_results = {
        'status': 'success',
        'message': 'Hardware test successful',
        'validations': [],
        'warnings': [],
        'data_received': data
    }
    
    required_fields = ['start_time', 'end_time', 'duration']
    for field in required_fields:
        if field not in data:
            validation_results['validations'].append(f"❌ Missing required field: {field}")
            validation_results['status'] = 'error'
        else:
            validation_results['validations'].append(f"✅ Field '{field}' present")
    
    if 'duration' in data:
        try:
            duration = int(data['duration'])
            if duration <= 0:
                validation_results['warnings'].append(f"⚠️ Duration is {duration}, should be positive")
            elif duration > 86400:
                validation_results['warnings'].append(f"⚠️ Duration is {duration}s (>24h), seems unusual")
        except ValueError:
            validation_results['validations'].append(f"❌ Duration must be a number")
            validation_results['status'] = 'error'
    
    for time_field in ['start_time', 'end_time']:
        if time_field in data:
            try:
                if data[time_field].endswith('Z'):
                    datetime.fromisoformat(data[time_field].replace('Z', '+00:00'))
                else:
                    datetime.fromisoformat(data[time_field])
            except ValueError:
                validation_results['validations'].append(f"❌ {time_field} format is invalid")
                validation_results['status'] = 'error'
    
    if validation_results['status'] == 'success':
        try:
            if supabase is None:
                validation_results['validations'].append("❌ Database not available")
                validation_results['status'] = 'error'
                return jsonify(validation_results), 500
            
            event_data = {
                "start_time": data['start_time'],
                "end_time": data['end_time'],
                "duration": int(data['duration'])
            }
            
            response = supabase.table('machine_events').insert(event_data).execute()
            if response.data:
                validation_results['validations'].append("✅ Data saved to database successfully")
                validation_results['database_record'] = response.data[0]
            else:
                validation_results['validations'].append("❌ Failed to save to database")
                validation_results['status'] = 'error'
        except Exception as db_error:
            validation_results['validations'].append(f"❌ Database error: {str(db_error)}")
            validation_results['status'] = 'error'
    
    status_code = 200 if validation_results['status'] == 'success' else 400
    return jsonify(validation_results), status_code

@app.route('/api/machine-data', methods=['GET', 'POST'])
@login_required
@handle_api_errors
def machine_data():
    if request.method == 'POST':
        data = request.get_json()
        logger.info(f"Received data: {data}")
        
        if data.get('api_key') != API_KEY:
            return jsonify({'error': 'Invalid API key'}), 401
        
        try:
            start_time = data['start_time']
            end_time = data['end_time']
            duration = int(data['duration'])
            machine_id = data.get('machine_id', '1')
            
            start_ist = convert_to_ist(start_time)
            end_ist = convert_to_ist(end_time)
            
            if start_ist and end_ist:
                start_time_db = start_ist.isoformat()
                end_time_db = end_ist.isoformat()
            else:
                start_time_db = start_time
                end_time_db = end_time
        except (KeyError, ValueError) as e:
            return jsonify({'error': 'Invalid data format', 'details': str(e)}), 400
        
        try:
            if supabase:
                response = supabase.table('machine_events').insert({
                    "start_time": start_time_db,
                    "end_time": end_time_db,
                    "duration": duration,
                    "machine_id": machine_id
                }).execute()

                if response.data:
                    logger.info(f"Data saved for {machine_id}")
                    return jsonify({'message': 'Data saved successfully'}), 200
                else:
                    return jsonify({'error': 'Supabase insert failed'}), 500
            else:
                # Offline mode - just acknowledge
                logger.info(f"Data received for {machine_id} (offline mode)")
                return jsonify({'message': 'Data received successfully (offline mode)'}), 200
        except Exception as e:
            logger.error(f"Database error: {e}")
            return jsonify({'error': 'Database error', 'details': str(e)}), 500
    else:
        try:
            # GET request - return machine data
            selected_machine = request.args.get('machine_id')
            user_machines = get_user_machine_filter()

            # If no specific machine selected, use all accessible machines
            if not selected_machine or selected_machine == 'all':
                machine_filter = user_machines
            else:
                # Validate user has access to selected machine
                if selected_machine not in user_machines:
                    return jsonify({'error': 'Access denied to selected machine'}), 403
                machine_filter = [selected_machine]

            if supabase:
                # Use real database
                query = supabase.table('machine_events').select("*")
                if machine_filter:
                    query = query.in_('machine_id', machine_filter)
                result = query.order('start_time', desc=True).limit(50).execute()
                events = result.data if result.data else []
            else:
                # Use mock data
                filtered_events = [e for e in mock_events if e['machine_id'] in machine_filter]
                events = filtered_events[:50]

            return jsonify({
                'machine_id': selected_machine or 'all',
                'machine_name': selected_machine or 'All Machines',
                'total_events': len(events),
                'events': events
            }), 200
        except Exception as e:
            logger.error(f"Machine data API error: {e}")
            return jsonify({'error': 'Database error', 'details': str(e)}), 500

@app.route('/api/health')
def health_check():
    health_status = {
        'status': 'healthy',
        'timestamp': datetime.now().isoformat(),
        'session_active': 'username' in session,
        'user': session.get('username', 'Not logged in'),
        'supabase_available': supabase is not None
    }
    
    try:
        if supabase:
            test_response = supabase.table('machine_events').select("*").limit(1).execute()
            health_status['database_records'] = len(test_response.data) if test_response.data else 0
    except Exception as e:
        health_status['database_error'] = str(e)
    
    return jsonify(health_status), 200

@app.route('/api/debug')
@login_required
@handle_api_errors
def debug_info():
    if supabase is None:
        db_status = "❌ Supabase not initialized"
        record_count = 0
    else:
        try:
            test_response = supabase.table('machine_events').select("*").limit(1).execute()
            db_status = "✅ Supabase connected"
            record_count = len(test_response.data) if test_response.data else 0
        except Exception as e:
            db_status = f"❌ Supabase error: {str(e)}"
            record_count = 0
    
    return jsonify({
        'session': {
            'username': session.get('username'),
            'role': session.get('role'),
            'machine_ids': session.get('machine_ids'),
            'machine_name': session.get('machine_name')
        },
        'database': {
            'status': db_status,
            'total_records': record_count
        },
        'timestamp': datetime.now().isoformat()
    }), 200

@app.route('/api/machine-data/<machine_id>', methods=['GET'])
@login_required
@handle_api_errors
def get_specific_machine_data(machine_id: str):
    if supabase is None:
        return jsonify({'error': 'Database not available'}), 500
    
    user_machines = get_user_machine_filter()
    if session.get('role') != 'admin' and machine_id not in user_machines:
        return jsonify({'error': 'Access denied to this machine'}), 403
    
    try:
        response = supabase.table('machine_events') \
            .select("*") \
            .eq('machine_id', machine_id) \
            .order("start_time", desc=True) \
            .execute() # type: ignore
        
        machine_name_map = {
            '1': 'machine_1',
            '2': 'machine_2',
            '3': 'machine_3',
            'machine_001': 'machine_1',  # Arduino format
            'machine_002': 'machine_2',
            'machine_003': 'machine_3',
            'machine_1': 'machine_1',  # Keep backward compatibility
            'machine_2': 'machine_2',
            'machine_3': 'machine_3'
        }
        
        events_with_info = []
        for event in response.data:
            event_copy = event.copy()
            event_copy['machine_name'] = machine_name_map.get(machine_id, machine_id)
            events_with_info.append(event_copy)
        
        return jsonify({
            'machine_id': machine_id,
            'machine_name': machine_name_map.get(machine_id, machine_id),
            'total_events': len(events_with_info),
            'events': events_with_info
        }), 200
    except Exception as e:
        logger.error(f"Database error: {e}")
        return jsonify({'error': 'Failed to fetch machine data', 'details': str(e)}), 500

@app.route('/api/machines/summary', methods=['GET'])
@login_required
@handle_api_errors
def get_machines_summary():
    if supabase is None:
        return jsonify({'error': 'Database not available'}), 500
    
    user_machines = get_user_machine_filter()
    machine_name_map = {
        '1': 'machine_1',
        '2': 'machine_2',
        '3': 'machine_3',
        'machine_001': 'machine_1',  # Arduino format
        'machine_002': 'machine_2',
        'machine_003': 'machine_3',
        'machine_1': 'machine_1',  # Keep backward compatibility
        'machine_2': 'machine_2',
        'machine_3': 'machine_3'
    }
    
    machines_summary = []
    for machine_id in user_machines:
        try:
            count_response = supabase.table('machine_events') \
                .select("*") \
                .eq('machine_id', machine_id) \
                .execute() # type: ignore
            
            runtime_response = supabase.table('machine_events') \
                .select("duration") \
                .eq('machine_id', machine_id) \
                .execute() # type: ignore
            
            total_runtime = sum(event.get('duration', 0) for event in runtime_response.data) if runtime_response.data else 0
            
            last_event_response = supabase.table('machine_events') \
                .select("*") \
                .eq('machine_id', machine_id) \
                .order("start_time", desc=True) \
                .limit(1) \
                .execute() # type: ignore
            
            last_event = last_event_response.data[0] if last_event_response.data else None
            
            machines_summary.append({
                'machine_id': machine_id,
                'machine_name': machine_name_map.get(machine_id, machine_id),
                'total_events': len(count_response.data) if count_response.data else 0,
                'total_runtime': total_runtime,
                'last_event': last_event
            })
        except Exception as e:
            logger.error(f"Error processing machine {machine_id}: {str(e)}")
            machines_summary.append({
                'machine_id': machine_id,
                'machine_name': machine_name_map.get(machine_id, machine_id),
                'total_events': 0,
                'total_runtime': 0,
                'last_event': None,
                'error': str(e)
            })
    
    return jsonify({
        'user': session['username'],
        'role': session['role'],
        'accessible_machines': len(user_machines),
        'machines': machines_summary
    }), 200

@app.route('/api/export/events-csv')
@login_required
@handle_api_errors
def export_events_csv():
    if supabase is None:
        return jsonify({'error': 'Database not available'}), 500
    
    machine_ids = get_user_machine_filter()
    query = supabase.table('machine_events').select("*")
    
    if session.get('role') != 'admin':
        query = query.in_('machine_id', machine_ids) # type: ignore
    
    response = query.order("start_time", desc=True).execute() # type: ignore
    events = response.data or []
    
    if not events:
        return jsonify({'error': 'No events found'}), 404
    
    csv_data = generate_events_csv(events)
    machine_suffix = session.get('machine_name', 'Machine').replace(' ', '_')
    filename = f"machine_events_{machine_suffix}_{get_ist_now().strftime('%Y%m%d_%H%M%S')}_IST.csv"
    
    return Response(
        csv_data,
        mimetype='text/csv',
        headers={'Content-Disposition': f'attachment; filename={filename}'}
    )

@app.route('/api/summary')
@login_required
def get_summary():
    try:
        # Get selected machine from query parameter
        selected_machine = request.args.get('machine_id')

        # Get user's machine filter
        machine_ids = get_user_machine_filter()

        # If no specific machine selected, use all accessible machines
        if not selected_machine or selected_machine == 'all':
            machine_filter = machine_ids
        else:
            # Validate user has access to selected machine
            if selected_machine not in machine_ids:
                return jsonify({'error': 'Access denied to selected machine'}), 403
            machine_filter = [selected_machine]

        if supabase:
            # Use real database
            base_table = supabase.table('machine_events')

            # Total events
            count_query = base_table.select("*")
            if machine_filter:
                count_query = count_query.in_('machine_id', machine_filter) # type: ignore
            count_resp = count_query.execute()  # type: ignore
            total_events = len(count_resp.data) if count_resp.data else 0

            # Total runtime
            runtime_query = base_table.select("duration")
            if machine_filter:
                runtime_query = runtime_query.in_('machine_id', machine_filter) # type: ignore
            runtime_resp = runtime_query.execute() # type: ignore
            total_runtime = sum(event['duration'] for event in runtime_resp.data) if runtime_resp.data else 0

            # Last event
            last_event_query = base_table.select("*")
            if machine_filter:
                last_event_query = last_event_query.in_('machine_id', machine_filter) # type: ignore
            last_event_resp = last_event_query.order("start_time", desc=True).limit(1).execute() # type: ignore
            last_event = last_event_resp.data[0] if last_event_resp.data else None
        else:
            # Use mock data (offline mode)
            filtered_events = [e for e in mock_events if e['machine_id'] in machine_filter]
            total_events = len(filtered_events)
            total_runtime = sum(event['duration'] for event in filtered_events)
            last_event = filtered_events[0] if filtered_events else None

        return jsonify({
            'total_events': total_events,
            'total_runtime': total_runtime,
            'last_event': last_event,
            'machine_filter': machine_filter
        }), 200
    except Exception as e:
        logger.error(f"Summary API error: {e}")
        return jsonify({'error': 'Database error', 'details': str(e)}), 500

@app.route('/api/charts/daily-runtime')
@login_required
def get_daily_runtime():
    labels = []
    data = []
    end_date = get_ist_now()

    for i in range(6, -1, -1):
        date = (end_date - timedelta(days=i)).date()
        labels.append(date.strftime('%a %m/%d'))
        data.append(0)

    if supabase is None:
        # Use mock data for offline mode
        import random
        for i in range(7):
            data[i] = random.randint(4, 12) * 3600  # 4-12 hours in seconds
        # Convert to hours
        data = [round(seconds / 3600, 1) for seconds in data]
        return jsonify({'labels': labels, 'data': data}), 200

    try:
        machine_id = request.args.get('machine_id')
        user_machines = get_user_machine_filter()
        base_query = supabase.table('machine_events').select("start_time, duration")

        if machine_id and machine_id != 'all':
            if session.get('role') != 'admin' and machine_id not in user_machines:
                return jsonify({'error': 'Access denied to this machine'}), 403
            base_query = base_query.eq('machine_id', machine_id) # type: ignore
        elif session.get('role') != 'admin' and user_machines:
            base_query = base_query.in_('machine_id', user_machines) # type: ignore

        start_date = end_date - timedelta(days=7)
        response = base_query.execute() # type: ignore

        for event in (response.data or []):
            try:
                start_time = event.get('start_time')
                if start_time:
                    ist_datetime = convert_to_ist(start_time)
                    if ist_datetime:
                        event_date = ist_datetime.date()

                        for i in range(7):
                            date = (end_date - timedelta(days=6-i)).date()
                            if date == event_date:
                                duration = event.get('duration', 0)
                                data[i] += duration / 3600
                                break
            except Exception as e:
                logger.warning(f"Skipping invalid event: {str(e)}")

        formatted_data = [round(x, 2) for x in data]
        return jsonify({'labels': labels, 'data': formatted_data}), 200
    except Exception as e:
        logger.error(f"Error in get_daily_runtime: {str(e)}")
        return jsonify({'labels': labels, 'data': data}), 200

@app.route('/api/charts/hourly-distribution')
@login_required
@handle_api_errors
def get_hourly_distribution():
    labels = [f"{i:02d}:00 IST" for i in range(24)]
    data = [0] * 24
    
    if supabase is None:
        # Use mock data for offline mode
        import random
        data = [random.randint(0, 10) for _ in range(24)]
        return jsonify({'labels': labels, 'data': data}), 200
    
    try:
        machine_id = request.args.get('machine_id')
        user_machines = get_user_machine_filter()
        base_query = supabase.table('machine_events').select("start_time, duration")

        if machine_id and machine_id != 'all':
            if session.get('role') != 'admin' and machine_id not in user_machines:
                return jsonify({'error': 'Access denied to this machine'}), 403
            base_query = base_query.eq('machine_id', machine_id) # type: ignore
        elif session.get('role') != 'admin' and user_machines:
            base_query = base_query.in_('machine_id', user_machines) # type: ignore

        end_date = get_ist_now()
        start_date = end_date - timedelta(days=30)
        response = base_query.execute() # type: ignore
        
        for event in (response.data or []):
            try:
                start_time = event.get('start_time')
                if start_time:
                    ist_datetime = convert_to_ist(start_time)
                    if ist_datetime:
                        hour = ist_datetime.hour
                        duration = event.get('duration', 0)
                        data[hour] += duration / 3600
            except Exception as e:
                logger.warning(f"Skipping invalid event: {str(e)}")
        
        formatted_data = [round(x, 2) for x in data]
        return jsonify({'labels': labels, 'data': formatted_data}), 200
    except Exception as e:
        logger.error(f"Error in get_hourly_distribution: {str(e)}")
        return jsonify({'labels': labels, 'data': data}), 200

@app.route('/api/charts/duration-distribution')
@login_required
@handle_api_errors
def get_duration_distribution():
    categories = {
        '< 1 min': 0,
        '1-5 min': 0,
        '5-15 min': 0,
        '15-30 min': 0,
        '30-60 min': 0,
        '> 1 hour': 0
    }
    
    if supabase is None:
        # Use mock data for offline mode
        import random
        mock_data = [random.randint(5, 25) for _ in range(6)]
        return jsonify({
            'labels': list(categories.keys()),
            'data': mock_data
        }), 200
    
    try:
        machine_id = request.args.get('machine_id')
        user_machines = get_user_machine_filter()
        query = supabase.table('machine_events').select("duration")

        if machine_id and machine_id != 'all':
            if session.get('role') != 'admin' and machine_id not in user_machines:
                return jsonify({'error': 'Access denied to this machine'}), 403
            query = query.eq('machine_id', machine_id) # type: ignore
        elif session.get('role') != 'admin' and user_machines:
            query = query.in_('machine_id', user_machines) # type: ignore

        response = query.execute() # type: ignore
        
        for event in (response.data or []):
            try:
                duration = event.get('duration', 0)
                duration_min = duration / 60
                
                if duration_min < 1:
                    categories['< 1 min'] += 1
                elif duration_min < 5:
                    categories['1-5 min'] += 1
                elif duration_min < 15:
                    categories['5-15 min'] += 1
                elif duration_min < 30:
                    categories['15-30 min'] += 1
                elif duration_min < 60:
                    categories['30-60 min'] += 1
                else:
                    categories['> 1 hour'] += 1
            except Exception as e:
                logger.warning(f"Skipping invalid event: {str(e)}")
        
        return jsonify({
            'labels': list(categories.keys()),
            'data': list(categories.values())
        }), 200
    except Exception as e:
        logger.error(f"Error in get_duration_distribution: {str(e)}")
        return jsonify({
            'labels': list(categories.keys()),
            'data': list(categories.values())
        }), 200

@app.route('/api/export/duration-distribution-csv')
@login_required
@handle_api_errors
def export_duration_distribution_csv():
    if supabase is None:
        return jsonify({'error': 'Database not available'}), 500
    
    machine_id = request.args.get('machine_id')
    user_machines = get_user_machine_filter()
    query = supabase.table('machine_events').select("duration")

    if machine_id and machine_id != 'all':
        if session.get('role') != 'admin' and machine_id not in user_machines:
            return jsonify({'error': 'Access denied to this machine'}), 403
        query = query.eq('machine_id', machine_id) # type: ignore
    elif session.get('role') != 'admin' and user_machines:
        query = query.in_('machine_id', user_machines) # type: ignore

    response = query.execute() # type: ignore
    
    categories = {
        '< 1 min': 0,
        '1-5 min': 0,
        '5-15 min': 0,
        '15-30 min': 0,
        '30-60 min': 0,
        '> 1 hour': 0
    }
    
    for event in (response.data or []):
        try:
            duration = event.get('duration', 0)
            duration_min = duration / 60
            
            if duration_min < 1:
                categories['< 1 min'] += 1
            elif duration_min < 5:
                categories['1-5 min'] += 1
            elif duration_min < 15:
                categories['5-15 min'] += 1
            elif duration_min < 30:
                categories['15-30 min'] += 1
            elif duration_min < 60:
                categories['30-60 min'] += 1
            else:
                categories['> 1 hour'] += 1
        except Exception:
            pass
    
    csv_data = generate_chart_csv(
        list(categories.keys()),
        list(categories.values()),
        'Duration Category'
    )
    filename = f"duration_distribution_{get_ist_now().strftime('%Y%m%d_%H%M%S')}_IST.csv"
    return Response(
        csv_data,
        mimetype='text/csv',
        headers={'Content-Disposition': f'attachment; filename={filename}'}
    )

if __name__ == '__main__':
    logger.info("🚀 Starting Machine Monitoring Flask App...")
    logger.info("🌐 Server will be available at:")
    logger.info("   - http://localhost:5000")
    logger.info("   - http://127.0.0.1:5000")
    logger.info("   - http://0.0.0.0:5000")
    logger.info("🛑 Press Ctrl+C to stop the server")
    
    try:
        app.run(host='0.0.0.0', port=5000, debug=True)
    except Exception as e:
        logger.error(f"❌ Error starting Flask app: {e}")
        logger.error(traceback.format_exc())

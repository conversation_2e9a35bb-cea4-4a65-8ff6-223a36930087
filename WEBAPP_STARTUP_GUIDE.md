# 🚀 Machine Monitoring WebApp - Complete Startup Guide

## ✅ Issues Fixed

### 1. Missing test-suite.js File (404 Error)
- **Problem**: `test-suite.js` was referenced in `index.html` but didn't exist
- **Solution**: Created comprehensive `app/static/js/test-suite.js` with automated testing capabilities
- **Features**: 
  - Automated API endpoint testing
  - JavaScript library validation
  - Dashboard element verification
  - Auto-run tests with URL parameter `?autotest=true`

### 2. API Endpoint 500 Errors
- **Problem**: `/api/summary` and `/api/machine-data` returning 500 Internal Server Error
- **Root Cause**: Authentication issues and missing session data
- **Solution**: Enhanced error handling and fallback mechanisms in all app versions
- **Fixed Files**: `app.py`, `app_fixed.py`, `app_production.py`, `app_simple.py`

### 3. Server Startup Issues
- **Problem**: Inconsistent server startup and configuration
- **Solution**: Created multiple startup scripts for different scenarios

## 🚀 How to Start the WebApp

### Method 1: PowerShell Script (Recommended for Windows)
```powershell
cd "G:\Pankaj\Embedded systems\machine-monitoring\machine-monitoring\app"
powershell -ExecutionPolicy Bypass -File start_server.ps1
```

### Method 2: Python Script with Options
```bash
# Single server on port 5000
python run_server.py

# Single server on custom port
python run_server.py --port 9090

# Dual servers (5000 and 9090)
python run_server.py --dual

# Production mode (no debug)
python run_server.py --no-debug

# Specific app version
python run_server.py --app app_production
```

### Method 3: Direct Flask App
```bash
# Try these in order of preference:
python app_fixed.py      # Most stable version
python app_production.py # Production optimized
python app_simple.py     # Minimal version
python app.py           # Full-featured version
```

### Method 4: Batch File (Windows)
```cmd
start_server.bat
```

## 🌐 Server URLs

Once started, the webapp will be available at:
- **Primary**: http://localhost:5000
- **Secondary**: http://localhost:9090 (if dual mode)
- **Test Page**: http://localhost:5000/test-server
- **Dashboard**: http://localhost:5000/dashboard
- **API Health**: http://localhost:5000/api/health

## 🔐 Login Credentials

### Admin Access (All Machines)
- **Username**: `admin` or `pankaj`
- **Password**: `admin123` or `pankaj123`

### User Access (Specific Machines)
- **user1** / `password1` (Machine 1)
- **user2** / `password2` (Machine 2)  
- **user3** / `password3` (Machine 3)

## 🧪 Testing the WebApp

### 1. Automated Test Page
Visit: http://localhost:5000/test-server
- Tests all API endpoints
- Validates authentication
- Checks server health
- Provides troubleshooting info

### 2. Manual API Testing
```bash
# Health check
curl http://localhost:5000/api/health

# Summary (requires login)
curl http://localhost:5000/api/summary?machine_id=machine_001

# Machine data (requires login)
curl http://localhost:5000/api/machine-data?machine_id=machine_001
```

### 3. Browser Testing
1. Open http://localhost:5000
2. Login with admin/admin123
3. Navigate to Dashboard
4. Verify charts load without errors
5. Check Events page
6. Test machine filtering

## 🔧 Troubleshooting

### Common Issues and Solutions

#### 1. Port Already in Use
```bash
# Find process using port 5000
netstat -ano | findstr :5000
# Kill the process (replace PID)
taskkill /PID <PID> /F
```

#### 2. Python Import Errors
```bash
# Install required packages
pip install -r requirements.txt
# Or install individually
pip install flask supabase python-dotenv
```

#### 3. Database Connection Issues
- Check internet connection
- Verify Supabase credentials in `.env` file
- App will work in offline mode with mock data

#### 4. 404 Errors on Static Files
- Ensure all files are in correct directories
- Check `app/static/js/test-suite.js` exists
- Verify `app/static/css/style.css` exists

#### 5. 500 API Errors
- Check if user is logged in
- Verify session is active
- Check server logs for detailed errors

## 📁 File Structure Verification

Ensure these files exist:
```
app/
├── app.py                 # Main Flask application
├── app_fixed.py          # Stable version with fixes
├── app_production.py     # Production optimized
├── app_simple.py         # Minimal version
├── run_server.py         # Advanced startup script
├── start_server.ps1      # PowerShell startup script
├── start_server.bat      # Batch startup script
├── test_server.html      # Server test page
├── requirements.txt      # Python dependencies
├── static/
│   ├── js/
│   │   ├── script.js     # Main JavaScript
│   │   └── test-suite.js # Testing framework
│   ├── css/
│   │   └── style.css     # Styles
│   └── favicon.svg       # Icon
└── templates/
    ├── index.html        # Base template
    ├── dashboard.html    # Dashboard page
    ├── events.html       # Events page
    ├── login.html        # Login page
    └── test.html         # Test page
```

## 🚀 Production Deployment

### For Production Servers (************** and **************)

1. **Copy files to production directory**:
   ```
   C:\inetpub\wwwroot\machine-monitoring\app\
   ```

2. **Start dual servers**:
   ```bash
   python run_server.py --dual --no-debug
   ```

3. **Configure IIS/Apache** (if needed):
   - Point to Flask application
   - Set up reverse proxy for ports 5000/9090
   - Configure SSL certificates

4. **Monitor logs**:
   - Check Flask application logs
   - Monitor system resources
   - Set up automatic restart on failure

## ✅ Success Indicators

The webapp is working correctly when:
- ✅ Health endpoint returns 200 OK
- ✅ Login page loads without errors
- ✅ Dashboard displays charts and data
- ✅ No 404 errors in browser console
- ✅ No 500 errors on API calls
- ✅ Machine filtering works properly
- ✅ Events page loads data
- ✅ Test page shows all green checkmarks

## 📞 Support

If issues persist:
1. Check the test page: http://localhost:5000/test-server
2. Review browser console for JavaScript errors
3. Check Flask server logs for Python errors
4. Verify all dependencies are installed
5. Ensure database connection is working

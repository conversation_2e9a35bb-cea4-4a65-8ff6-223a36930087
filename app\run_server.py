#!/usr/bin/env python3
"""
Production-ready Flask server startup script
Handles multiple server configurations and error recovery
"""
import sys
import os
import time
import threading
import argparse
from datetime import datetime

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def print_banner():
    """Print startup banner"""
    print("=" * 60)
    print("🚀 MACHINE MONITORING SYSTEM")
    print("=" * 60)
    print(f"📅 Started: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"📁 Directory: {os.getcwd()}")
    print(f"🐍 Python: {sys.version.split()[0]}")
    print("=" * 60)

def start_server(port=5000, app_module='app_fixed', debug=True):
    """Start Flask server with error handling"""
    try:
        print(f"🔄 Importing {app_module}...")

        if app_module == 'app_fixed':
            from app_fixed import app
        elif app_module == 'app_production':
            from app_production import app
        elif app_module == 'app_simple':
            from app_simple import app
        else:
            from app import app

        print(f"✅ {app_module} imported successfully")
        print(f"🌐 Starting server on:")
        print(f"   - http://localhost:{port}")
        print(f"   - http://127.0.0.1:{port}")
        print(f"   - http://0.0.0.0:{port}")
        print("🛑 Press Ctrl+C to stop")
        print("-" * 60)

        app.run(host='0.0.0.0', port=port, debug=debug, threaded=True)

    except ImportError as e:
        print(f"❌ Import error for {app_module}: {e}")
        return False
    except Exception as e:
        print(f"❌ Error starting {app_module} on port {port}: {e}")
        import traceback
        traceback.print_exc()
        return False

    return True

def start_dual_servers():
    """Start servers on both ports 5000 and 9090"""
    print("🔄 Starting dual server configuration...")

    def server_5000():
        print("🚀 Starting server on port 5000...")
        start_server(port=5000, app_module='app_fixed', debug=False)

    def server_9090():
        print("🚀 Starting server on port 9090...")
        time.sleep(2)  # Slight delay to avoid conflicts
        start_server(port=9090, app_module='app_production', debug=False)

    # Start both servers in separate threads
    thread1 = threading.Thread(target=server_5000, daemon=True)
    thread2 = threading.Thread(target=server_9090, daemon=True)

    thread1.start()
    thread2.start()

    try:
        # Keep main thread alive
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        print("\n🛑 Shutting down servers...")
        sys.exit(0)

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='Machine Monitoring Flask Server')
    parser.add_argument('--port', type=int, default=5000, help='Port to run server on')
    parser.add_argument('--app', default='app_fixed', help='App module to use')
    parser.add_argument('--dual', action='store_true', help='Start dual servers on ports 5000 and 9090')
    parser.add_argument('--no-debug', action='store_true', help='Disable debug mode')

    args = parser.parse_args()

    print_banner()

    if args.dual:
        start_dual_servers()
    else:
        # Try the specified app first
        success = start_server(
            port=args.port,
            app_module=args.app,
            debug=not args.no_debug
        )

        if not success:
            print("💡 Trying fallback options...")
            fallback_apps = ['app_fixed', 'app_production', 'app_simple', 'app']
            for fallback in fallback_apps:
                if fallback != args.app:
                    print(f"🔄 Trying {fallback}...")
                    if start_server(port=args.port, app_module=fallback, debug=not args.no_debug):
                        break
            else:
                print("❌ All startup attempts failed!")
                sys.exit(1)

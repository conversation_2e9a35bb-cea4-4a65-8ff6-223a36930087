/**
 * Test Suite for Machine Monitoring System
 * Provides testing utilities and automated test functions
 */

class TestSuite {
    constructor() {
        this.tests = [];
        this.results = [];
        this.isRunning = false;
        console.log('🧪 Test Suite initialized');
    }

    /**
     * Add a test to the suite
     */
    addTest(name, testFunction, description = '') {
        this.tests.push({
            name,
            testFunction,
            description,
            status: 'pending'
        });
    }

    /**
     * Run all tests
     */
    async runAllTests() {
        if (this.isRunning) {
            console.warn('Tests are already running');
            return;
        }

        this.isRunning = true;
        this.results = [];
        console.log('🚀 Starting test suite...');

        for (const test of this.tests) {
            try {
                console.log(`🧪 Running test: ${test.name}`);
                const startTime = Date.now();
                await test.testFunction();
                const duration = Date.now() - startTime;
                
                test.status = 'passed';
                this.results.push({
                    name: test.name,
                    status: 'passed',
                    duration,
                    message: 'Test passed successfully'
                });
                console.log(`✅ Test passed: ${test.name} (${duration}ms)`);
            } catch (error) {
                test.status = 'failed';
                this.results.push({
                    name: test.name,
                    status: 'failed',
                    duration: 0,
                    message: error.message,
                    error
                });
                console.error(`❌ Test failed: ${test.name}`, error);
            }
        }

        this.isRunning = false;
        this.displayResults();
        console.log('🏁 Test suite completed');
    }

    /**
     * Display test results
     */
    displayResults() {
        const passed = this.results.filter(r => r.status === 'passed').length;
        const failed = this.results.filter(r => r.status === 'failed').length;
        
        console.log(`📊 Test Results: ${passed} passed, ${failed} failed`);
        
        // If there's a results container on the page, update it
        const resultsContainer = document.getElementById('testResults');
        if (resultsContainer) {
            resultsContainer.innerHTML = this.generateResultsHTML();
        }
    }

    /**
     * Generate HTML for test results
     */
    generateResultsHTML() {
        const passed = this.results.filter(r => r.status === 'passed').length;
        const failed = this.results.filter(r => r.status === 'failed').length;
        
        let html = `
            <div class="test-summary mb-3">
                <h5>Test Summary</h5>
                <div class="row">
                    <div class="col-md-6">
                        <div class="alert alert-success">
                            <i class="bi bi-check-circle me-2"></i>
                            Passed: ${passed}
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="alert alert-danger">
                            <i class="bi bi-x-circle me-2"></i>
                            Failed: ${failed}
                        </div>
                    </div>
                </div>
            </div>
            <div class="test-details">
        `;

        this.results.forEach(result => {
            const alertClass = result.status === 'passed' ? 'alert-success' : 'alert-danger';
            const icon = result.status === 'passed' ? 'check-circle' : 'x-circle';
            
            html += `
                <div class="alert ${alertClass} mb-2">
                    <i class="bi bi-${icon} me-2"></i>
                    <strong>${result.name}:</strong> ${result.message}
                    ${result.duration ? `<small class="text-muted ms-2">(${result.duration}ms)</small>` : ''}
                </div>
            `;
        });

        html += '</div>';
        return html;
    }

    /**
     * Test API endpoint
     */
    async testApiEndpoint(url, expectedStatus = 200) {
        const response = await fetch(url);
        if (response.status !== expectedStatus) {
            throw new Error(`Expected status ${expectedStatus}, got ${response.status}`);
        }
        return response;
    }

    /**
     * Test if element exists
     */
    testElementExists(selector) {
        const element = document.querySelector(selector);
        if (!element) {
            throw new Error(`Element not found: ${selector}`);
        }
        return element;
    }

    /**
     * Test if function exists
     */
    testFunctionExists(functionName, context = window) {
        if (typeof context[functionName] !== 'function') {
            throw new Error(`Function not found: ${functionName}`);
        }
        return context[functionName];
    }
}

// Create global test suite instance
window.testSuite = new TestSuite();

// Add basic tests
window.testSuite.addTest('API Health Check', async () => {
    await window.testSuite.testApiEndpoint('/api/health');
});

window.testSuite.addTest('Dashboard Elements', () => {
    // Only run if we're on the dashboard page
    if (document.querySelector('.dashboard')) {
        window.testSuite.testElementExists('#machineSelect');
        window.testSuite.testElementExists('#summaryCards');
    }
});

window.testSuite.addTest('JavaScript Libraries', () => {
    // Test if required libraries are loaded
    if (typeof jQuery === 'undefined') {
        throw new Error('jQuery not loaded');
    }
    if (typeof Chart === 'undefined') {
        throw new Error('Chart.js not loaded');
    }
    if (typeof bootstrap === 'undefined') {
        throw new Error('Bootstrap not loaded');
    }
});

window.testSuite.addTest('Main App Instance', () => {
    window.testSuite.testFunctionExists('machineMonitoringApp', window);
});

// Auto-run tests if in test mode (can be controlled via URL parameter)
if (window.location.search.includes('autotest=true')) {
    document.addEventListener('DOMContentLoaded', () => {
        setTimeout(() => window.testSuite.runAllTests(), 1000);
    });
}

console.log('🧪 Test Suite loaded successfully');

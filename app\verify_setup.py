#!/usr/bin/env python3
"""
Machine Monitoring System - Setup Verification Script
This script verifies that all components are properly configured
"""

import os
import sys
import importlib.util
from pathlib import Path

def print_header(title):
    """Print a formatted header"""
    print("\n" + "=" * 60)
    print(f"🔍 {title}")
    print("=" * 60)

def check_file(filepath, description):
    """Check if a file exists"""
    if os.path.exists(filepath):
        print(f"✅ {description}: {filepath}")
        return True
    else:
        print(f"❌ {description}: {filepath} (MISSING)")
        return False

def check_python_module(module_name):
    """Check if a Python module can be imported"""
    try:
        __import__(module_name)
        print(f"✅ Python module: {module_name}")
        return True
    except ImportError:
        print(f"❌ Python module: {module_name} (NOT INSTALLED)")
        return False

def check_flask_app(app_file):
    """Check if a Flask app can be imported"""
    try:
        spec = importlib.util.spec_from_file_location("app", app_file)
        if spec is None:
            print(f"❌ Flask app: {app_file} (INVALID)")
            return False
        
        module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(module)
        
        if hasattr(module, 'app'):
            print(f"✅ Flask app: {app_file}")
            return True
        else:
            print(f"❌ Flask app: {app_file} (NO APP OBJECT)")
            return False
    except Exception as e:
        print(f"❌ Flask app: {app_file} (ERROR: {e})")
        return False

def main():
    """Main verification function"""
    print("🚀 Machine Monitoring System - Setup Verification")
    print(f"📁 Current directory: {os.getcwd()}")
    print(f"🐍 Python version: {sys.version}")
    
    all_good = True
    
    # Check required files
    print_header("Required Files Check")
    
    required_files = [
        ("app.py", "Main Flask application"),
        ("app_fixed.py", "Fixed Flask application"),
        ("app_production.py", "Production Flask application"),
        ("app_simple.py", "Simple Flask application"),
        ("requirements.txt", "Python dependencies"),
        ("static/js/script.js", "Main JavaScript file"),
        ("static/js/test-suite.js", "Test suite JavaScript"),
        ("static/css/style.css", "CSS styles"),
        ("templates/index.html", "Base HTML template"),
        ("templates/dashboard.html", "Dashboard template"),
        ("templates/login.html", "Login template"),
        ("test_server.html", "Server test page"),
        ("run_server.py", "Advanced startup script"),
        ("start_server.ps1", "PowerShell startup script"),
        ("start_server.bat", "Batch startup script")
    ]
    
    for filepath, description in required_files:
        if not check_file(filepath, description):
            all_good = False
    
    # Check Python modules
    print_header("Python Dependencies Check")
    
    required_modules = [
        "flask",
        "supabase",
        "dotenv",
        "datetime",
        "os",
        "hashlib",
        "logging",
        "functools"
    ]
    
    for module in required_modules:
        if not check_python_module(module):
            all_good = False
    
    # Check Flask applications
    print_header("Flask Applications Check")
    
    flask_apps = [
        "app.py",
        "app_fixed.py", 
        "app_production.py",
        "app_simple.py"
    ]
    
    working_apps = []
    for app_file in flask_apps:
        if os.path.exists(app_file):
            if check_flask_app(app_file):
                working_apps.append(app_file)
        else:
            print(f"⚠️  Flask app: {app_file} (FILE NOT FOUND)")
    
    if not working_apps:
        print("❌ No working Flask applications found!")
        all_good = False
    
    # Check startup scripts
    print_header("Startup Scripts Check")
    
    startup_scripts = [
        ("run_server.py", "Advanced Python startup script"),
        ("start_server.ps1", "PowerShell startup script"),
        ("start_server.bat", "Batch startup script")
    ]
    
    for script, description in startup_scripts:
        check_file(script, description)
    
    # Final summary
    print_header("Verification Summary")
    
    if all_good:
        print("🎉 ALL CHECKS PASSED!")
        print("\n✅ Your Machine Monitoring System is ready to run!")
        print("\n🚀 To start the server, use one of these methods:")
        print("   1. python run_server.py")
        print("   2. powershell -ExecutionPolicy Bypass -File start_server.ps1")
        print("   3. start_server.bat")
        if working_apps:
            print(f"   4. python {working_apps[0]}")
        
        print("\n🌐 Once started, visit:")
        print("   - http://localhost:5000 (Main application)")
        print("   - http://localhost:5000/test-server (Test page)")
        print("   - http://localhost:5000/api/health (Health check)")
        
        print("\n🔐 Login with:")
        print("   - admin / admin123 (Full access)")
        print("   - pankaj / pankaj123 (Full access)")
        
    else:
        print("❌ SOME CHECKS FAILED!")
        print("\n🔧 Please fix the issues above before running the server.")
        print("\n💡 Common solutions:")
        print("   - Install missing Python packages: pip install -r requirements.txt")
        print("   - Ensure all files are in the correct directories")
        print("   - Check file permissions")
        
    print("\n" + "=" * 60)
    return all_good

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

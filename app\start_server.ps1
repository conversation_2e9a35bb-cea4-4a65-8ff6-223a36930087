# Machine Monitoring System - PowerShell Startup Script
# This script starts the Flask application with proper error handling

Write-Host "=" -ForegroundColor Cyan -NoNewline
Write-Host ("=" * 59) -ForegroundColor Cyan
Write-Host "🚀 MACHINE MONITORING SYSTEM STARTUP" -ForegroundColor Green
Write-Host "=" -ForegroundColor Cyan -NoNewline
Write-Host ("=" * 59) -ForegroundColor Cyan

$startTime = Get-Date
Write-Host "📅 Started: $($startTime.ToString('yyyy-MM-dd HH:mm:ss'))" -ForegroundColor Yellow
Write-Host "📁 Directory: $(Get-Location)" -ForegroundColor Yellow
Write-Host "🐍 Python: $(python --version 2>$null)" -ForegroundColor Yellow

Write-Host "`n🔍 Checking Python installation..." -ForegroundColor Cyan

# Check if Python is available
try {
    $pythonVersion = python --version 2>$null
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ Python found: $pythonVersion" -ForegroundColor Green
    } else {
        throw "Python not found"
    }
} catch {
    Write-Host "❌ Python not found in PATH" -ForegroundColor Red
    Write-Host "💡 Please install Python or add it to your PATH" -ForegroundColor Yellow
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host "`n🔍 Checking required files..." -ForegroundColor Cyan

$appFiles = @("app_fixed.py", "app.py", "app_production.py", "app_simple.py")
$foundApp = $null

foreach ($file in $appFiles) {
    if (Test-Path $file) {
        Write-Host "✅ Found: $file" -ForegroundColor Green
        if ($null -eq $foundApp) {
            $foundApp = $file
        }
    } else {
        Write-Host "⚠️  Missing: $file" -ForegroundColor Yellow
    }
}

if ($null -eq $foundApp) {
    Write-Host "❌ No Flask app files found!" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host "`n🚀 Starting Flask server with $foundApp..." -ForegroundColor Green
Write-Host "🌐 Server will be available at:" -ForegroundColor Cyan
Write-Host "   - http://localhost:5000" -ForegroundColor White
Write-Host "   - http://127.0.0.1:5000" -ForegroundColor White
Write-Host "   - http://0.0.0.0:5000" -ForegroundColor White
Write-Host "`n🛑 Press Ctrl+C to stop the server" -ForegroundColor Red
Write-Host "-" -ForegroundColor Cyan -NoNewline
Write-Host ("-" * 59) -ForegroundColor Cyan

# Function to start server
function Start-FlaskServer {
    param($AppFile)
    
    try {
        Write-Host "`n🔄 Starting $AppFile..." -ForegroundColor Yellow
        python $AppFile
    } catch {
        Write-Host "❌ Error starting $AppFile`: $_" -ForegroundColor Red
        return $false
    }
    return $true
}

# Try to start the server with the found app
$success = Start-FlaskServer -AppFile $foundApp

if (-not $success) {
    Write-Host "`n💡 Trying alternative apps..." -ForegroundColor Yellow
    
    foreach ($file in $appFiles) {
        if ($file -ne $foundApp -and (Test-Path $file)) {
            Write-Host "🔄 Trying $file..." -ForegroundColor Yellow
            $success = Start-FlaskServer -AppFile $file
            if ($success) {
                break
            }
        }
    }
}

if (-not $success) {
    Write-Host "`n❌ All startup attempts failed!" -ForegroundColor Red
    Write-Host "💡 Please check the error messages above" -ForegroundColor Yellow
}

Write-Host "`n📊 Session ended at: $(Get-Date)" -ForegroundColor Yellow
Read-Host "Press Enter to exit"

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Machine Monitoring - Server Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        .warning { background-color: #fff3cd; border-color: #ffeaa7; }
        .info { background-color: #d1ecf1; border-color: #bee5eb; }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background-color: #0056b3; }
        .result {
            margin-top: 10px;
            padding: 10px;
            border-radius: 3px;
            font-family: monospace;
            white-space: pre-wrap;
        }
        .loading {
            color: #007bff;
            font-style: italic;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 Machine Monitoring System - Server Test</h1>
        <p>This page tests the Flask server endpoints to ensure everything is working correctly.</p>
        
        <div class="test-section info">
            <h3>📊 Server Status</h3>
            <button onclick="testHealth()">Test Health Endpoint</button>
            <div id="healthResult" class="result"></div>
        </div>
        
        <div class="test-section info">
            <h3>🔐 Authentication Test</h3>
            <p>Test login functionality:</p>
            <input type="text" id="username" placeholder="Username (try: admin)" value="admin">
            <input type="password" id="password" placeholder="Password (try: admin123)" value="admin123">
            <button onclick="testLogin()">Test Login</button>
            <div id="loginResult" class="result"></div>
        </div>
        
        <div class="test-section info">
            <h3>📈 API Endpoints Test</h3>
            <button onclick="testSummary()">Test Summary API</button>
            <button onclick="testMachineData()">Test Machine Data API</button>
            <button onclick="testAllEndpoints()">Test All Endpoints</button>
            <div id="apiResult" class="result"></div>
        </div>
        
        <div class="test-section info">
            <h3>🌐 Navigation Test</h3>
            <button onclick="openDashboard()">Open Dashboard</button>
            <button onclick="openEvents()">Open Events</button>
            <button onclick="openTest()">Open Test Page</button>
        </div>
        
        <div class="test-section warning">
            <h3>⚠️ Troubleshooting</h3>
            <p>If tests fail, check:</p>
            <ul>
                <li>Flask server is running on port 5000</li>
                <li>No firewall blocking connections</li>
                <li>Database connection is working</li>
                <li>All required Python packages are installed</li>
            </ul>
        </div>
    </div>

    <script>
        async function testHealth() {
            const result = document.getElementById('healthResult');
            result.textContent = 'Testing health endpoint...';
            result.className = 'result loading';
            
            try {
                const response = await fetch('/api/health');
                const data = await response.json();
                
                if (response.ok) {
                    result.textContent = `✅ Health check passed!\n${JSON.stringify(data, null, 2)}`;
                    result.className = 'result success';
                } else {
                    result.textContent = `❌ Health check failed: ${response.status}\n${JSON.stringify(data, null, 2)}`;
                    result.className = 'result error';
                }
            } catch (error) {
                result.textContent = `❌ Network error: ${error.message}`;
                result.className = 'result error';
            }
        }
        
        async function testLogin() {
            const result = document.getElementById('loginResult');
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            
            result.textContent = 'Testing login...';
            result.className = 'result loading';
            
            try {
                const formData = new FormData();
                formData.append('username', username);
                formData.append('password', password);
                
                const response = await fetch('/login', {
                    method: 'POST',
                    body: formData
                });
                
                if (response.ok) {
                    result.textContent = `✅ Login successful! Redirected to: ${response.url}`;
                    result.className = 'result success';
                } else {
                    result.textContent = `❌ Login failed: ${response.status}`;
                    result.className = 'result error';
                }
            } catch (error) {
                result.textContent = `❌ Login error: ${error.message}`;
                result.className = 'result error';
            }
        }
        
        async function testSummary() {
            const result = document.getElementById('apiResult');
            result.textContent = 'Testing summary API...';
            result.className = 'result loading';
            
            try {
                const response = await fetch('/api/summary?machine_id=machine_001');
                const data = await response.json();
                
                if (response.ok) {
                    result.textContent = `✅ Summary API working!\n${JSON.stringify(data, null, 2)}`;
                    result.className = 'result success';
                } else {
                    result.textContent = `❌ Summary API failed: ${response.status}\n${JSON.stringify(data, null, 2)}`;
                    result.className = 'result error';
                }
            } catch (error) {
                result.textContent = `❌ Summary API error: ${error.message}`;
                result.className = 'result error';
            }
        }
        
        async function testMachineData() {
            const result = document.getElementById('apiResult');
            result.textContent = 'Testing machine data API...';
            result.className = 'result loading';
            
            try {
                const response = await fetch('/api/machine-data?machine_id=machine_001');
                const data = await response.json();
                
                if (response.ok) {
                    result.textContent = `✅ Machine Data API working!\n${JSON.stringify(data, null, 2)}`;
                    result.className = 'result success';
                } else {
                    result.textContent = `❌ Machine Data API failed: ${response.status}\n${JSON.stringify(data, null, 2)}`;
                    result.className = 'result error';
                }
            } catch (error) {
                result.textContent = `❌ Machine Data API error: ${error.message}`;
                result.className = 'result error';
            }
        }
        
        async function testAllEndpoints() {
            const result = document.getElementById('apiResult');
            result.textContent = 'Testing all API endpoints...';
            result.className = 'result loading';
            
            const endpoints = [
                '/api/health',
                '/api/summary',
                '/api/machine-data',
                '/api/charts/daily-runtime',
                '/api/charts/hourly-distribution',
                '/api/charts/duration-distribution'
            ];
            
            let results = [];
            
            for (const endpoint of endpoints) {
                try {
                    const response = await fetch(endpoint);
                    const status = response.ok ? '✅' : '❌';
                    results.push(`${status} ${endpoint}: ${response.status}`);
                } catch (error) {
                    results.push(`❌ ${endpoint}: ${error.message}`);
                }
            }
            
            result.textContent = results.join('\n');
            result.className = 'result info';
        }
        
        function openDashboard() {
            window.open('/dashboard', '_blank');
        }
        
        function openEvents() {
            window.open('/events', '_blank');
        }
        
        function openTest() {
            window.open('/test', '_blank');
        }
        
        // Auto-run health check on page load
        window.addEventListener('load', testHealth);
    </script>
</body>
</html>
